import { getToken } from '@/utils/auth'
import type * as T from './type'
import http from '@/utils/http'
import { baseOptions, ContentType } from './type'
import handleStream from '@/views/app/workflow/utils/handle-stream'
export type * from './type'

const BASE_URL = 'console/api'

/** @desc workflow详情 */
export function getWorkflow(id: string) {
  return http.get<T.FetchWorkflowDraftResponseType>(`${BASE_URL}/apps/${id}/workflows/draft`)
}
/** @desc workflow保存 */
export function saveWorkflow(id: string, data) {
  return http.post(`${BASE_URL}/apps/${id}/workflows/draft`, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
/** @desc workflow执行记录 */
export function getWorkflowRuns(id: string) {
  return http.get(`${BASE_URL}/apps/${id}/workflow-runs`)
}
/** @desc workflow发布 */
export function workflowPublish(id: string) {
  return http.get(`${BASE_URL}/apps/${id}/workflows/publish`)
}
/** @desc workflow发布为工具 */
export function publishToll(data) {
  return http.post(`${BASE_URL}/workspaces/current/tool-provider/workflow/create`, data)
}
/** @desc workflow发布为工具-工作流 */
export function toolsworkflow() {
  return http.get(`${BASE_URL}/workspaces/current/tools/workflow`)
}
/** @desc workflow更新工具 */
export function updatePublishToll(data) {
  return http.post(`${BASE_URL}/workspaces/current/tool-provider/workflow/update`, data)
}
/** @desc workflow获取工具详情 */
export function getPublishToll(data) {
  return http.get(`${BASE_URL}/workspaces/current/tool-provider/workflow/get`, data)
}
/** @desc workflow config */
export function draftConfig(id: string) {
  return http.get(`${BASE_URL}/apps/${id}/workflows/draft/config`)
}
/** @desc workflow default config */
export function defaultConfig(id: string) {
  return http.get(`${BASE_URL}/apps/${id}/workflows/default-workflow-block-configs`)
}
/** @desc workflow info */
export function getWorkflowInfo(id: string) {
  return http.get(`${BASE_URL}/apps/${id}`)
}
export function workflowRun(appId, data: object) {
  return http.post(`${BASE_URL}/apps/${appId}/workflows/draft/run`, data)
}
export function siteEnable(appId, data: object) {
  return http.post(`${BASE_URL}/apps/${appId}/site-enable`, data)
}
export function apiEnable(appId, data: object) {
  return http.post(`${BASE_URL}/apps/${appId}/api-enable`, data)
}
export function workflowhistoryRun(appId, id) {
  return http.get(`${BASE_URL}/apps/${appId}/workflow-runs/${id}`)
}
export function workflownodeexecutions(appId, id) {
  return http.get(`${BASE_URL}/apps/${appId}/workflow-runs/${id}/node-executions`)
}
export function dailyConversations(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/statistics/daily-conversations?start=${params.start}&end=${params.end}`)
}
export function dailyEndUsers(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/statistics/daily-end-users?start=${params.start}&end=${params.end}`)
}
export function averageSessionInteractions(appId, params) {
  return http.get(
    `${BASE_URL}/apps/${appId}/statistics/average-session-interactions?start=${params.start}&end=${params.end}`
  )
}
export function tokensPerSecond(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/statistics/tokens-per-second?start=${params.start}&end=${params.end}`)
}
export function userSatisfactionRate(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/statistics/user-satisfaction-rate?start=${params.start}&end=${params.end}`)
}
export function tokenCosts(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/statistics/token-costs?start=${params.start}&end=${params.end}`)
}
export function dailyMessages(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/statistics/daily-messages?start=${params.start}&end=${params.end}`)
}
export function workflowConversations(appId, params) {
  return http.get(
    `${BASE_URL}/apps/${appId}/workflow/statistics/daily-conversations?start=${params.start}&end=${params.end}`
  )
}
export function workflowTerminals(appId, params) {
  return http.get(
    `${BASE_URL}/apps/${appId}/workflow/statistics/daily-terminals?start=${params.start}&end=${params.end}`
  )
}
export function workflowCosts(appId, params) {
  return http.get(`${BASE_URL}/apps/${appId}/workflow/statistics/token-costs?start=${params.start}&end=${params.end}`)
}
export function workflowinteractions(appId, params) {
  return http.get(
    `${BASE_URL}/apps/${appId}/workflow/statistics/average-app-interactions?start=${params.start}&end=${params.end}`
  )
}
export function accessTokenReset(appId, data: object) {
  return http.post(`${BASE_URL}/apps/${appId}//site/access-token-reset`, data)
}
export function siteSubmit(appId, data: object) {
  return http.post(`${BASE_URL}/apps/${appId}/site`, data)
}
export function workflowRunFetch(appId, fetchOptions: T.FetchOptionType, otherOptions?: T.IOtherOptions) {
  const {
    onData,
    onCompleted,
    onFile,
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onNodeRetry,
    onParallelBranchStarted,
    onParallelBranchFinished,
    onTextChunk,
    onTTSChunk,
    onTTSEnd,
    onTextReplace,
    onAgentLog,
    onError,
    // getAbortController,
    onLoopStart,
    onLoopNext,
    onLoopFinish
  } = otherOptions!
  const abortController = new AbortController()
  const token = getToken()
  const options = Object.assign(
    {},
    baseOptions,
    {
      method: 'POST',
      signal: abortController.signal,
      headers: new Headers({
        Authorization: `Bearer ${token}`
      })
    } as RequestInit,
    fetchOptions
  )
  const contentType = (options.headers as Headers).get('Content-Type')
  if (!contentType) (options.headers as Headers).set('Content-Type', ContentType.json)
  const { body } = options
  if (body) options.body = JSON.stringify(body)
  fetch(`/console/api/apps/${appId}/workflows/draft/run`, options as RequestInit)
    .then((res) => {
      return handleStream(
        res,
        (str: string, isFirstMessage: boolean, moreInfo: T.IOnDataMoreInfo) => {
          if (moreInfo.errorMessage) {
            onError?.(moreInfo.errorMessage, moreInfo.errorCode)
            if (
              moreInfo.errorMessage !== 'AbortError: The user aborted a request.' &&
              !moreInfo.errorMessage.includes('TypeError: Cannot assign to read only property')
            )
              console.warn({ type: 'error', message: moreInfo.errorMessage })
            return
          }

          onData?.(str, isFirstMessage, moreInfo)
        },
        onCompleted,
        onFile,
        onWorkflowStarted,
        onWorkflowFinished,
        onNodeStarted,
        onNodeFinished,
        onIterationStart,
        onIterationNext,
        onIterationFinish,
        onLoopStart,
        onLoopNext,
        onLoopFinish,
        onNodeRetry,
        onParallelBranchStarted,
        onParallelBranchFinished,
        onTextChunk,
        onTTSChunk,
        onTTSEnd,
        onTextReplace,
        onAgentLog
      )
    })
    .catch((e) => {
      if (
        e.toString() !== 'AbortError: The user aborted a request.' &&
        !e.toString().errorMessage.includes('TypeError: Cannot assign to read only property')
      )
        console.log('err')
    })
}
