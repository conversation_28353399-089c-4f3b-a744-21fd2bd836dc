<template>
  <a-scrollbar outer-style="height: 100%" style="height: 100%; overflow-y: auto">
    <div style="padding: 6px">
      <a-row :gutter="[12, 12]">
        <a-col
          v-for="(item, index) in appListData.data"
          :key="index"
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="8"
          :xxl="8"
          class="card-col"
        >
          <a-card :bordered="true" hoverable>
            <a-card-meta>
              <template #title>
                <div class="card-title" @click="onChat(item)">
                  <a-space :size="20">
                    <a-badge color="#00B42A" :count="9" dot :dot-style="{ width: '10px', height: '10px' }">
                      <a-avatar class="badgetyle" shape="square">
                        <span>
                          <AiSvgIcon :name="`workflow-ai-${item.mode}`" />
                        </span>
                      </a-avatar>
                    </a-badge>
                    <div :style="{ height: '44px' }">
                      <a-typography-paragraph
                        :style="{ fontSize: '16px' }"
                        :ellipsis="{
                          rows: 1,
                          showTooltip: true,
                          css: true
                        }"
                      >
                        {{ item.name }}
                      </a-typography-paragraph>
                      <a-typography-text
                        :style="{ fontSize: '12px' }"
                        :ellipsis="{
                          rows: 1,
                          showTooltip: true,
                          css: true
                        }"
                        type="secondary"
                      >
                        {{ flowType[item.mode] }}
                      </a-typography-text>
                    </div>
                  </a-space>
                  <!-- <a-dropdown class="more-icon" trigger="hover" @select="handleSelect($event, item)">
                    <IconMore />
                    <template #content>
                      <a-doption :value="{ value: 'edit' }">编排</a-doption>
                      <a-doption :value="{ value: 'export' }">导出</a-doption>
                      <a-doption :value="{ value: 'copy' }">复制</a-doption>
                      <a-doption :value="{ value: 'del' }">删除</a-doption>
                    </template>
</a-dropdown> -->
                </div>
              </template>
              <template #description>
                <a-typography-paragraph
                  :style="{ height: '44px', marginTop: '10px' }"
                  :ellipsis="{
                    rows: 2,
                    showTooltip: false,
                    css: true
                  }"
                  @click="onChat(item)"
                >
                  <a-typography-text type="secondary">
                    {{ item.description }}
                  </a-typography-text>
                </a-typography-paragraph>
              </template>

              <template #avatar>
                <div :style="{ display: 'flex', alignItems: 'center' }" @click="handleClick">
                  <span class="button-hover">
                    <a-space wrap>
                      <a-popover
                        trigger="click"
                        position="bottom"
                        :popup-visible="activeDropdownDatasetId === item.id"
                        @popup-visible-change="(visible) => handlePopoverVisibleChange(visible, item.id)"
                      >
                        <a-tag
                          :style="{
                            backgroundColor: 'var(--color-fill-2)',
                            border: '1px dashed var(--color-fill-3)',
                            cursor: 'pointer'
                          }"
                        >
                          <template #icon>
                            <icon-plus />
                          </template>
                          标签
                        </a-tag>

                        <template #content>
                          <div style="width: 240px">
                            <div style="margin-bottom: 8px">
                              <a-input
                                v-model="tagSearchText"
                                placeholder="搜索或者创建"
                                allow-clear
                                @keyup.enter="handleCreateTag"
                              >
                                <template #prefix>
                                  <icon-search />
                                </template>
                              </a-input>
                            </div>
                            <div style="max-height: 200px; overflow-y: auto">
                              <div v-for="tag in filteredTags" :key="tag.id" style="padding: 6px 0; cursor: pointer">
                                <a-checkbox
                                  :model-value="isTagSelected(tag, item)"
                                  @change="(checked) => handleTagCheck(checked, tag, item)"
                                >
                                  {{ tag.name }}
                                </a-checkbox>
                              </div>
                            </div>
                          </div>
                        </template>
                      </a-popover>
                      <a-tag
                        v-for="tagItem of item.tags"
                        :key="tagItem.id"
                        :closable="true"
                        @close="handleRemove(tagItem, item)"
                      >
                        {{ tagItem.name }}
                      </a-tag>
                    </a-space>
                  </span>
                </div>
              </template>
            </a-card-meta>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </a-scrollbar>
</template>

<script lang="ts" setup>
import { deleteApp, exportAppConfig, createTagBinding, removeTags, createDatasetTag } from '@/apis'
import { Modal, Message } from '@arco-design/web-vue'
import { install } from 'vue-codemirror6'

const emit = defineEmits<{
  (e: 'operate-success'): void
}>()
const props = defineProps({
  appListData: {
    type: Object,
    default() {
      return {}
    }
  },
  tags: {
    type: Array,
    default() {
      return []
    }
  },
  installAppListData: {
    type: Array,
    default() {
      return []
    }
  }
})
const flowType = {
  workflow: '工作流',
  'agent-chat': 'AGENT',
  chat: '聊天助手',
  'advanced-chat': 'CHATFLOW',
  completion: '文本生成'
}
const router = useRouter()

const handleClick = (event) => {
  event.stopPropagation()
}

const availableTags = ref()
const activeDropdownDatasetId = ref<string | null>(null)
const tagSearchText = ref('')

const fetchAvailableTags = async () => {
  try {
    // const tags = await getTags({ type: 'app' })
    const tags = props.tags
    availableTags.value = tags
  } catch (error) {
    Message.error('获取标签失败')
  }
}

// 过滤标签列表
const filteredTags = computed(() => {
  const searchText = tagSearchText.value.toLowerCase().trim()
  if (!searchText) return availableTags.value
  return availableTags.value.filter((tag) => tag.name.toLowerCase().includes(searchText))
})

// 处理 popover 可见性变化
const handlePopoverVisibleChange = (visible: boolean, itemId: string) => {
  if (visible) {
    activeDropdownDatasetId.value = itemId
    tagSearchText.value = ''
  } else {
    activeDropdownDatasetId.value = null
  }
}

// 检查标签是否已选中
const isTagSelected = (tag, item) => {
  if (!item.tags) return false
  return item.tags.some((t) => t.id === tag.id)
}

// 处理标签勾选
const handleTagCheck = async (checked: boolean, tag, item) => {
  if (!item.tags) {
    item.tags = []
  }
  const index = item.tags.findIndex((t) => t.id === tag.id)
  try {
    if (checked) {
      if (index === -1) {
        const params = {
          tag_ids: [tag.id],
          target_id: item.id,
          type: 'app'
        }
        await createTagBinding(params)
        item.tags.push({
          id: tag.id,
          name: tag.name,
          type: 'app'
        })

        Message.success(`已添加标签"${tag.name}"`)
      }
    } else {
      if (index > -1) {
        const params = {
          tag_id: tag.id,
          target_id: item.id,
          type: 'app'
        }
        await removeTags(params)
        item.tags.splice(index, 1)
        Message.success(`已移除标签"${tag.name}"`)
      }
    }
  } catch (error) {
    Message.error('更新应用标签失败')
    emit('operate-success')
  }
}

// 创建新标签
const handleCreateTag = async () => {
  const tagName = tagSearchText.value.trim()
  if (!tagName) return

  // 检查标签是否已存在
  if (availableTags.value.some((t) => t.name.toLowerCase() === tagName.toLowerCase())) {
    Message.warning('标签已存在')
    return
  }
  try {
    const newTag = await createDatasetTag({ name: tagName })
    availableTags.value.push(newTag)
    tagSearchText.value = ''
    Message.success('标签创建成功')
  } catch (error) {
    Message.error('创建标签失败')
  }
}

watchEffect(() => {
  if (props.tags) {
    fetchAvailableTags()
  }
})
const handleRemove = async (tag, item) => {
  try {
    const params = {
      tag_id: tag.id,
      target_id: item.id,
      type: 'app'
    }

    await removeTags(params)

    if (item.tags) {
      item.tags = item.tags.filter((t) => t.id !== tag.id)
    }

    Message.success(`已移除标签"${tag.name}"`)
  } catch (error) {
    Message.error('移除标签失败')
  }
}
const delhandleClick = (id) => {
  Modal.warning({
    title: '确认删除应用?',
    titleAlign: 'start',
    hideCancel: false,
    content: () =>
      h('div', { class: 'info-modal-content' }, [
        h('span', { style: 'margin-bottom: 10px;' }, '删除应用将无法撤销，用户将不能访问你的应用。')
      ]),
    onOk: () => {
      onDelApp(id)
    }
  })
}
const onDelApp = async (id) => {
  await deleteApp(id)
  Message.success('删除成功')
  emit('operate-success')
}
const onExportApp = async (item) => {
  try {
    const { data } = await exportAppConfig({
      id: item.id,
      include: false
    })
    const a = document.createElement('a')
    const file = new Blob([data.toString()], { type: 'application/yaml' })
    a.href = URL.createObjectURL(file)
    a.download = `${item.name}.yml`
    a.click()
    Message.success('导出成功')
  } catch {
    Message.error('导出失败')
  }
}
const goWorkflow = (v, item) => {
  router.push({
    path: `/appsManage/flow/${item.id}/${item.mode === 'workflow' || item.mode === 'advanced-chat' ? 'workflow' : 'configuration'}`
  })
}
// const handleSelect = (v, item) => {
//   if (v.value === 'edit') {
//     goWorkflow(v, item)
//   } else if (v.value === '2') {
//     console.log('停用')
//   } else if (v.value === 'export') {
//     onExportApp(item)
//   } else if (v.value === 'copy') {
//     onCopy(item)
//   } else if (v.value === 'del') {
//     delhandleClick(item.id)
//   }
// }
// chat
const onChat = (record) => {
  // 如果是 advanced-chat 模式，跳转到 chatflow 页面
  if (record.mode === 'advanced-chat') {
    router.push({ path: `/appsManage/chatflow/${record.id}` })
    return
  }

  // 其他模式跳转到原来的探索页面
  const app = props.installAppListData?.find((ele: any) => ele.app.id === record.id) as any
  if (app) {
    router.push({ path: `/workspace/ai/explore/${app.id}` })
  }
}
</script>

<style scoped lang="less">
:deep(.arco-card-bordered) {
  border-radius: 5px;
  cursor: pointer;

  &:hover {
    border-color: rgb(var(--primary-6));

    .button-hover .arco-btn,
    .more-icon {
      display: block;
    }
  }
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .more-icon {
    display: none;
  }
}

.badgetyle {
  height: 36px;
  width: 36px;
  border-radius: 4px;
  background: linear-gradient(rgb(120, 87, 252) 0%, rgb(214, 130, 229) 100%);
}

.button-hover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 24px;
  // border-radius: 50%;
  transition: all 1s;
  animation: button-hover-animated 2s ease-in-out infinite;

  .arco-btn {
    color: #ffffff;
    display: none;
    background-color: rgb(var(--primary-6));
  }
}
</style>
