import { ref } from 'vue'
import type {
  WorkflowStartedResponse,
  WorkflowFinishedResponse,
  NodeStartedResponse,
  NodeFinishedResponse,
  TextChunkResponse,
  TextReplaceResponse,
  AgentLogResponse,
  IterationStartedResponse,
  IterationNextResponse,
  IterationFinishedResponse,
  LoopStartedResponse,
  LoopNextResponse,
  LoopFinishedResponse,
  ParallelBranchStartedResponse,
  ParallelBranchFinishedResponse
} from '@/views/app/workflow/types/workflow'

export interface ChatflowMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  isStreaming?: boolean
  status?: 'running' | 'success' | 'error'
  timestamp: number
}

export interface ChatflowNodeStatus {
  id: string
  title: string
  type: string
  status: 'running' | 'success' | 'error' | 'stopped'
  startTime?: number
  endTime?: number
  elapsed_time?: number
  error?: string
}

export interface ChatflowRunState {
  isRunning: boolean
  workflowRunId?: string
  messages: ChatflowMessage[]
  nodeStatuses: Map<string, ChatflowNodeStatus>
  currentNodeId?: string
  error?: string
  result?: any
}

export function useChatflowRunEvent() {
  const runState = ref<ChatflowRunState>({
    isRunning: false,
    messages: [],
    nodeStatuses: new Map()
  })

  // 重置运行状态
  const resetRunState = () => {
    runState.value = {
      isRunning: false,
      messages: [],
      nodeStatuses: new Map()
    }
  }

  // 添加消息
  const addMessage = (message: Omit<ChatflowMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatflowMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: Date.now()
    }
    runState.value.messages.push(newMessage)
    return newMessage
  }

  // 更新最后一条消息
  const updateLastMessage = (updates: Partial<ChatflowMessage>) => {
    const messages = runState.value.messages
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1]
      Object.assign(lastMessage, updates)
    }
  }

  // 处理工作流开始
  const handleWorkflowStarted = (data: WorkflowStartedResponse) => {
    console.log('Chatflow 工作流开始:', data)
    runState.value.isRunning = true
    runState.value.workflowRunId = data.workflow_run_id
    runState.value.nodeStatuses.clear()

    addMessage({
      role: 'system',
      content: '工作流开始执行...',
      status: 'running'
    })
  }

  // 处理工作流完成
  const handleWorkflowFinished = (data: WorkflowFinishedResponse) => {
    console.log('Chatflow 工作流完成:', data)
    runState.value.isRunning = false
    runState.value.currentNodeId = undefined
    runState.value.result = data.data

    // 更新系统消息状态
    const systemMessage = runState.value.messages.find((m) => m.role === 'system' && m.status === 'running')
    if (systemMessage) {
      systemMessage.content = '工作流执行完成'
      systemMessage.status = data.data.status === 'succeeded' ? 'success' : 'error'
    }

    // 完成最后一条流式消息
    const lastMessage = runState.value.messages[runState.value.messages.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
      lastMessage.isStreaming = false
    }
  }

  // 处理工作流失败
  const handleWorkflowFailed = (error?: string) => {
    console.log('Chatflow 工作流失败:', error)

    // 防止无限递归
    if (!runState.value.isRunning) {
      return
    }

    runState.value.isRunning = false
    runState.value.currentNodeId = undefined
    runState.value.error = error

    // 更新系统消息状态
    const systemMessage = runState.value.messages.find((m) => m.role === 'system' && m.status === 'running')
    if (systemMessage) {
      systemMessage.content = `工作流执行失败: ${error || '未知错误'}`
      systemMessage.status = 'error'
    }

    // 更新最后一条助手消息
    const lastMessage = runState.value.messages[runState.value.messages.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
      lastMessage.content = '抱歉，处理您的请求时出现了错误。'
      lastMessage.isStreaming = false
    }
  }

  // 处理节点开始
  const handleNodeStarted = (data: NodeStartedResponse) => {
    console.log('Chatflow 节点开始:', data)
    const nodeId = data.data.node_id
    const nodeStatus: ChatflowNodeStatus = {
      id: nodeId,
      title: data.data.title,
      type: data.data.node_type,
      status: 'running',
      startTime: Date.now()
    }

    runState.value.nodeStatuses.set(nodeId, nodeStatus)
    runState.value.currentNodeId = nodeId
  }

  // 处理节点完成
  const handleNodeFinished = (data: NodeFinishedResponse) => {
    console.log('Chatflow 节点完成:', data)
    const nodeId = data.data.node_id
    const nodeStatus = runState.value.nodeStatuses.get(nodeId)

    if (nodeStatus) {
      nodeStatus.status = data.data.status === 'succeeded' ? 'success' : 'error'
      nodeStatus.endTime = Date.now()
      nodeStatus.elapsed_time = data.data.elapsed_time
      nodeStatus.error = data.data.error
    }

    if (runState.value.currentNodeId === nodeId) {
      runState.value.currentNodeId = undefined
    }
  }

  // 处理文本块
  const handleTextChunk = (data: TextChunkResponse) => {
    console.log('Chatflow 文本块:', data)
    const text = data.data.text

    // 查找最后一条助手消息
    let lastAssistantMessage = runState.value.messages
      .slice()
      .reverse()
      .find((m) => m.role === 'assistant')

    if (!lastAssistantMessage) {
      // 如果没有助手消息，创建一个新的
      lastAssistantMessage = addMessage({
        role: 'assistant',
        content: text,
        isStreaming: true
      })
    } else {
      // 追加文本到现有消息
      lastAssistantMessage.content += text
      lastAssistantMessage.isStreaming = true
    }
  }

  // 处理文本替换
  const handleTextReplace = (data: TextReplaceResponse) => {
    console.log('Chatflow 文本替换:', data)
    const text = data.data.text

    // 查找最后一条助手消息
    const lastAssistantMessage = runState.value.messages
      .slice()
      .reverse()
      .find((m) => m.role === 'assistant')

    if (lastAssistantMessage) {
      lastAssistantMessage.content = text
      lastAssistantMessage.isStreaming = true
    } else {
      // 如果没有助手消息，创建一个新的
      addMessage({
        role: 'assistant',
        content: text,
        isStreaming: true
      })
    }
  }

  // 处理代理日志
  const handleAgentLog = (data: AgentLogResponse) => {
    console.log('Chatflow 代理日志:', data)
    // 可以根据需要处理代理日志
  }

  // 处理迭代开始
  const handleIterationStarted = (data: IterationStartedResponse) => {
    console.log('Chatflow 迭代开始:', data)
    // 可以根据需要处理迭代状态
  }

  // 处理迭代下一步
  const handleIterationNext = (data: IterationNextResponse) => {
    console.log('Chatflow 迭代下一步:', data)
    // 可以根据需要处理迭代状态
  }

  // 处理迭代完成
  const handleIterationFinished = (data: IterationFinishedResponse) => {
    console.log('Chatflow 迭代完成:', data)
    // 可以根据需要处理迭代状态
  }

  // 处理循环开始
  const handleLoopStarted = (data: LoopStartedResponse) => {
    console.log('Chatflow 循环开始:', data)
    // 可以根据需要处理循环状态
  }

  // 处理循环下一步
  const handleLoopNext = (data: LoopNextResponse) => {
    console.log('Chatflow 循环下一步:', data)
    // 可以根据需要处理循环状态
  }

  // 处理循环完成
  const handleLoopFinished = (data: LoopFinishedResponse) => {
    console.log('Chatflow 循环完成:', data)
    // 可以根据需要处理循环状态
  }

  // 处理并行分支开始
  const handleParallelBranchStarted = (data: ParallelBranchStartedResponse) => {
    console.log('Chatflow 并行分支开始:', data)
    // 可以根据需要处理并行分支状态
  }

  // 处理并行分支完成
  const handleParallelBranchFinished = (data: ParallelBranchFinishedResponse) => {
    console.log('Chatflow 并行分支完成:', data)
    // 可以根据需要处理并行分支状态
  }

  // 获取当前节点状态
  const getCurrentNodeStatus = () => {
    if (!runState.value.currentNodeId) return null
    return runState.value.nodeStatuses.get(runState.value.currentNodeId) || null
  }

  // 获取所有节点状态
  const getAllNodeStatuses = () => {
    return Array.from(runState.value.nodeStatuses.values())
  }

  return {
    runState: readonly(runState),
    resetRunState,
    addMessage,
    updateLastMessage,
    handleWorkflowStarted,
    handleWorkflowFinished,
    handleWorkflowFailed,
    handleNodeStarted,
    handleNodeFinished,
    handleTextChunk,
    handleTextReplace,
    handleAgentLog,
    handleIterationStarted,
    handleIterationNext,
    handleIterationFinished,
    handleLoopStarted,
    handleLoopNext,
    handleLoopFinished,
    handleParallelBranchStarted,
    handleParallelBranchFinished,
    getCurrentNodeStatus,
    getAllNodeStatuses
  }
}
