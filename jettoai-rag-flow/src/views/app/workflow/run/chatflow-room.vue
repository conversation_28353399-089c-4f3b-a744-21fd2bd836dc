<template>
  <div class="chatflow-room">
    <!-- 变量输入表单 -->
    <div v-if="showVariableForm && variables.length > 0" class="variable-form">
      <div class="form-header">请填写以下变量:</div>
      <div class="form-content">
        <div v-for="variable in variables" :key="variable.variable" class="form-item">
          <label class="form-label">{{ variable.label }}</label>
          <a-input
            v-if="variable.type === 'text-input'"
            v-model="variableValues[variable.variable]"
            :placeholder="variable.label"
            :required="variable.required"
            size="small"
          />
          <a-textarea
            v-else-if="variable.type === 'paragraph'"
            v-model="variableValues[variable.variable]"
            :placeholder="variable.label"
            :required="variable.required"
            :rows="3"
            size="small"
          />
          <a-select
            v-else-if="variable.type === 'select'"
            v-model="variableValues[variable.variable]"
            :placeholder="variable.label"
            :required="variable.required"
            size="small"
          >
            <a-option v-for="option in variable.options" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-option>
          </a-select>
          <a-input-number
            v-else-if="variable.type === 'number'"
            v-model="variableValues[variable.variable]"
            :placeholder="variable.label"
            :required="variable.required"
            size="small"
            class="w-full"
          />
          <a-upload
            v-else-if="variable.type === 'file'"
            v-model:file-list="variableValues[variable.variable]"
            :limit="1"
            :auto-upload="false"
            :show-file-list="true"
            accept="*"
          >
            <template #upload-button>
              <a-button size="small">
                <template #icon>
                  <icon-upload />
                </template>
                选择文件
              </a-button>
            </template>
          </a-upload>
          <a-upload
            v-else-if="variable.type === 'file-list'"
            v-model:file-list="variableValues[variable.variable]"
            :auto-upload="false"
            :show-file-list="true"
            multiple
            accept="*"
          >
            <template #upload-button>
              <a-button size="small">
                <template #icon>
                  <icon-upload />
                </template>
                选择文件
              </a-button>
            </template>
          </a-upload>
        </div>
      </div>
      <div class="form-actions">
        <a-button type="primary" size="small" :loading="isExecuting" @click="startWorkflow">开始执行</a-button>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div ref="messagesContainer" class="messages-container">
      <div v-if="messages.length === 0" class="empty-state">
        <div class="empty-icon">💬</div>
        <div class="empty-text">开始对话以测试您的 Chatflow</div>
      </div>

      <div v-for="(message, index) in messages" :key="index" class="message-item">
        <!-- 用户消息 -->
        <div v-if="message.role === 'user'" class="flex justify-end mb-4">
          <div class="bg-blue-500 text-white rounded-lg px-4 py-2 max-w-xs lg:max-w-md">
            {{ message.content }}
          </div>
        </div>

        <!-- 助手消息 -->
        <div v-else-if="message.role === 'assistant'" class="flex justify-start mb-4">
          <div class="bg-gray-200 text-gray-800 rounded-lg px-4 py-2 max-w-xs lg:max-w-md">
            <div v-if="message.isStreaming" class="flex items-center">
              <div class="typing-indicator">
                <span />
                <span />
                <span />
              </div>
            </div>
            <div v-else>
              <div v-html="formatMessage(message.content)" />
            </div>
          </div>
        </div>

        <!-- 系统状态消息 -->
        <div v-else-if="message.role === 'system'" class="flex justify-center mb-4">
          <div class="bg-yellow-100 border border-yellow-300 rounded-lg px-4 py-2 text-sm">
            <div class="flex items-center space-x-2">
              <icon-loading v-if="message.status === 'running'" class="animate-spin" />
              <icon-check-circle v-else-if="message.status === 'success'" class="text-green-500" />
              <icon-exclamation-circle v-else-if="message.status === 'error'" class="text-red-500" />
              <span>{{ message.content }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 节点执行状态 -->
      <div v-if="currentNodeStatus" class="flex justify-center mb-4">
        <div class="bg-blue-100 border border-blue-300 rounded-lg px-4 py-2 text-sm">
          <div class="flex items-center space-x-2">
            <icon-loading class="animate-spin text-blue-500" />
            <span>正在执行: {{ currentNodeStatus.title }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="border-t p-4">
      <!-- 变量输入表单 -->
      <div v-if="showVariableForm && variables.length > 0" class="mb-4">
        <div class="text-sm font-medium mb-2">请填写以下变量:</div>
        <div class="space-y-3">
          <div v-for="variable in variables" :key="variable.variable" class="flex flex-col">
            <label class="text-sm text-gray-600 mb-1">{{ variable.label }}</label>
            <a-input
              v-if="variable.type === 'text-input'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
            />
            <a-textarea
              v-else-if="variable.type === 'paragraph'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              :rows="3"
            />
            <a-select
              v-else-if="variable.type === 'select'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
            >
              <a-option v-for="option in variable.options" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-option>
            </a-select>
          </div>
        </div>
        <div class="flex justify-end mt-3">
          <a-button type="primary" :loading="isExecuting" @click="startWorkflow">开始执行</a-button>
        </div>
      </div>

      <!-- 普通聊天输入 -->
      <div v-else class="flex space-x-2">
        <a-input
          v-model="inputMessage"
          placeholder="输入消息..."
          :disabled="isExecuting"
          class="flex-1"
          @keyup.enter="sendMessage"
        />
        <a-button
          type="primary"
          :disabled="!inputMessage.trim() || isExecuting"
          :loading="isExecuting"
          @click="sendMessage"
        >
          发送
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { workflowRunFetch } from '@/apis/workflow'
import { chatflowRunFetch } from '@/apis/workflow/chatflow'
import { Message } from '@arco-design/web-vue'
import { AppMode } from '@/apis/apps/type'
import { useAppStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useChatflowRunEvent } from '../hooks/use-chatflow-run-event'

interface Props {
  appId: string
  variables: any[]
  isRunning: boolean
}

const props = defineProps<Props>()
const emits = defineEmits(['start-execution', 'stop-execution'])

const appStore = useAppStore()
const { currentAppInfo } = storeToRefs(appStore)

// 使用 chatflow 运行事件处理器
const {
  runState,
  addMessage,
  updateLastMessage,
  handleWorkflowStarted,
  handleWorkflowFinished,
  handleWorkflowFailed,
  handleNodeStarted,
  handleNodeFinished,
  handleTextChunk,
  handleTextReplace,
  handleAgentLog,
  getCurrentNodeStatus
} = useChatflowRunEvent()

const inputMessage = ref('')
const messagesContainer = ref()
const showVariableForm = ref(true)
const variableValues = ref<Record<string, any>>({})
const abortController = ref<AbortController | null>(null)

// 判断是否为 advanced-chat 模式
const isAdvancedChat = computed(() => {
  return currentAppInfo.value?.mode === AppMode.ADVANCED_CHAT
})

// 从运行状态中获取数据
const messages = computed(() => runState.value.messages)
const isExecuting = computed(() => runState.value.isRunning)
const currentNodeStatus = computed(() => getCurrentNodeStatus())

// 初始化变量默认值
onMounted(() => {
  initializeVariables()
})

const initializeVariables = () => {
  props.variables.forEach((variable) => {
    if (variable.default) {
      variableValues.value[variable.variable] = variable.default
    }
  })
}

const formatMessage = (content: string) => {
  // 简单的 markdown 格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const validateVariables = () => {
  for (const variable of props.variables) {
    if (variable.required && !variableValues.value[variable.variable]) {
      Message.error(`请填写必填字段: ${variable.label}`)
      return false
    }
  }
  return true
}

const startWorkflow = async () => {
  if (!validateVariables()) return

  showVariableForm.value = false
  emits('start-execution')

  scrollToBottom()

  try {
    await executeWorkflow(variableValues.value)
  } catch (error) {
    console.error('工作流执行失败:', error)
    handleWorkflowFailed('工作流执行失败')
  }
}

const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息
  addMessage({
    role: 'user',
    content: userMessage
  })

  scrollToBottom()
  emits('start-execution')

  try {
    // 构建输入参数
    const inputs = { ...variableValues.value }

    // 查找查询变量
    const queryVar = props.variables.find(
      (v: any) =>
        v.variable === 'query' || v.variable === 'input' || v.variable === 'question' || v.variable === 'message'
    )

    if (queryVar) {
      inputs[queryVar.variable] = userMessage
    } else if (props.variables.length > 0) {
      // 如果没有明确的查询变量，使用第一个文本变量
      const firstTextVar = props.variables.find((v: any) => v.type === 'text-input' || v.type === 'paragraph')
      if (firstTextVar) {
        inputs[firstTextVar.variable] = userMessage
      }
    }

    // 确保有 query 字段传递给后端
    if (!inputs.query && !inputs.input) {
      inputs.query = userMessage
    }

    await executeWorkflow(inputs)
  } catch (error) {
    console.error('消息发送失败:', error)
    handleWorkflowFailed('消息发送失败')
  }
}

const executeWorkflow = async (inputs: Record<string, any>) => {
  try {
    if (isAdvancedChat.value) {
      abortController.value = chatflowRunFetch(
        props.appId,
        {
          body: { inputs, files: [] }
        },
        {
          onWorkflowStarted: handleWorkflowStarted,
          onWorkflowFinished: (data) => {
            handleWorkflowFinished(data)
            emits('stop-execution')

            // 添加执行完成的反馈消息
            if (data.data?.status === 'succeeded') {
              addMessage({
                role: 'system',
                content: '✅ 工作流执行成功',
                status: 'success'
              })
            } else {
              addMessage({
                role: 'system',
                content: '❌ 工作流执行失败',
                status: 'error'
              })
            }

            scrollToBottom()
          },
          onNodeStarted: (data) => {
            handleNodeStarted(data)
            scrollToBottom()
          },
          onNodeFinished: handleNodeFinished,
          onTextChunk: (data) => {
            handleTextChunk(data)
            scrollToBottom()
          },
          onTextReplace: (data) => {
            handleTextReplace(data)
            scrollToBottom()
          },
          onAgentLog: handleAgentLog,
          onError: (error) => {
            console.error('工作流错误:', error)
            handleWorkflowFailed(error)
            emits('stop-execution')
          }
        }
      )
    } else {
      workflowRunFetch(
        props.appId,
        {
          body: { inputs, files: [] }
        },
        {
          onWorkflowStarted: handleWorkflowStarted,
          onWorkflowFinished: (data) => {
            handleWorkflowFinished(data)
            emits('stop-execution')

            // 添加执行完成的反馈消息
            if (data.data?.status === 'succeeded') {
              addMessage({
                role: 'system',
                content: '✅ 工作流执行成功',
                status: 'success'
              })
            } else {
              addMessage({
                role: 'system',
                content: '❌ 工作流执行失败',
                status: 'error'
              })
            }

            scrollToBottom()
          },
          onNodeStarted: (data) => {
            handleNodeStarted(data)
            scrollToBottom()
          },
          onNodeFinished: handleNodeFinished,
          onTextChunk: (data) => {
            handleTextChunk(data)
            scrollToBottom()
          },
          onTextReplace: (data) => {
            handleTextReplace(data)
            scrollToBottom()
          },
          onAgentLog: handleAgentLog,
          onError: (error) => {
            console.error('工作流错误:', error)
            handleWorkflowFailed(error)
            emits('stop-execution')
          }
        }
      )
    }
  } catch (error) {
    console.error('执行工作流时出错:', error)
    handleWorkflowFailed('执行失败')
  }
}

const stopExecution = async () => {
  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }

  // 使用事件处理器处理停止
  handleWorkflowFailed('执行已停止')

  // 更新流式消息状态
  updateLastMessage({
    content: (messages.value[messages.value.length - 1]?.content || '') + '\n\n[执行已停止]',
    isStreaming: false
  })

  emits('stop-execution')
  scrollToBottom()
}

defineExpose({
  stopExecution
})
</script>

<style scoped lang="scss">
.chatflow-room {
  height: 100%;
  display: flex;
  flex-direction: column;

  .variable-form {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;

    .form-header {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 12px;
    }

    .form-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .form-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .form-label {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
      }
    }

    .form-actions {
      margin-top: 12px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #9ca3af;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 8px;
      }

      .empty-text {
        font-size: 14px;
      }
    }

    .message-item {
      margin-bottom: 16px;
    }
  }

  .chat-input {
    padding: 16px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .typing-indicator {
    display: flex;
    align-items: center;

    span {
      height: 4px;
      width: 4px;
      background-color: #9ca3af;
      border-radius: 50%;
      display: inline-block;
      margin-right: 2px;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
