<template>
  <div class="chatflow-container h-full flex">
    <!-- 左侧输入面板 -->
    <div class="input-panel w-96 border-r border-gray-200 flex flex-col">
      <!-- 头部信息 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div
            class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
          >
            <icon-robot class="text-white text-lg" />
          </div>
          <div>
            <h3 class="font-semibold text-gray-900">{{ appInfo?.name || 'Chatflow 应用' }}</h3>
            <p class="text-sm text-gray-500">{{ appInfo?.description || '智能对话流应用' }}</p>
          </div>
        </div>
      </div>

      <!-- 变量输入表单 -->
      <div class="flex-1 p-4 overflow-y-auto">
        <div v-if="variables.length > 0" class="space-y-4">
          <div class="text-sm font-medium text-gray-700 mb-3">输入变量</div>
          <div v-for="variable in variables" :key="variable.variable" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ variable.label }}
              <span v-if="variable.required" class="text-red-500">*</span>
            </label>

            <!-- 文本输入 -->
            <a-input
              v-if="variable.type === 'text-input'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              :max-length="variable.max_length"
              show-word-limit
            />

            <!-- 段落输入 -->
            <a-textarea
              v-else-if="variable.type === 'paragraph'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              :max-length="variable.max_length"
              :rows="4"
              show-word-limit
            />

            <!-- 选择器 -->
            <a-select
              v-else-if="variable.type === 'select'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
            >
              <a-option v-for="option in variable.options" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-option>
            </a-select>

            <!-- 数字输入 -->
            <a-input-number
              v-else-if="variable.type === 'number'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              class="w-full"
            />

            <div v-if="variable.description" class="text-xs text-gray-500">
              {{ variable.description }}
            </div>
          </div>
        </div>

        <div v-else class="text-center text-gray-500 mt-8">
          <icon-info-circle class="text-4xl mb-2" />
          <p>此应用无需输入变量</p>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="p-4 border-t border-gray-200">
        <a-button type="primary" long :loading="isStarting" :disabled="!canStart" @click="startConversation">
          <template #icon>
            <icon-play-arrow />
          </template>
          开始对话
        </a-button>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-area flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <div class="chat-header p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <icon-message class="text-gray-600" />
            <span class="font-medium text-gray-900">对话</span>
          </div>
          <div class="flex items-center space-x-2">
            <a-button v-if="isExecuting" size="small" status="danger" :loading="stopLoading" @click="stopExecution">
              <template #icon>
                <icon-stop />
              </template>
              停止
            </a-button>
            <a-button size="small" @click="clearConversation">
              <template #icon>
                <icon-refresh />
              </template>
              清空
            </a-button>
          </div>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div ref="messagesContainer" class="chat-messages flex-1 overflow-y-auto p-4">
        <div v-if="messages.length === 0" class="text-center text-gray-500 mt-16">
          <icon-robot class="text-6xl mb-4 text-gray-300" />
          <p class="text-lg">欢迎使用 Chatflow</p>
          <p class="text-sm">请先填写左侧的输入变量，然后开始对话</p>
        </div>

        <div v-for="(message, index) in messages" :key="index" class="message-item mb-6">
          <!-- 用户消息 -->
          <div v-if="message.role === 'user'" class="flex justify-end">
            <div class="bg-blue-500 text-white rounded-2xl px-4 py-2 max-w-md shadow-sm">
              {{ message.content }}
            </div>
          </div>

          <!-- 助手消息 -->
          <div v-else-if="message.role === 'assistant'" class="flex justify-start">
            <div class="bg-white border border-gray-200 rounded-2xl px-4 py-2 max-w-md shadow-sm">
              <div v-if="message.isStreaming" class="flex items-center space-x-2">
                <div class="typing-indicator">
                  <span />
                  <span />
                  <span />
                </div>
                <span class="text-gray-500 text-sm">正在思考...</span>
              </div>
              <div v-else class="prose prose-sm max-w-none" v-html="formatMessage(message.content)" />
            </div>
          </div>

          <!-- 系统状态消息 -->
          <div v-else-if="message.role === 'system'" class="flex justify-center">
            <div class="bg-gray-100 border border-gray-200 rounded-lg px-3 py-2 text-sm text-gray-600">
              <div class="flex items-center space-x-2">
                <icon-loading v-if="message.status === 'running'" class="animate-spin text-blue-500" />
                <icon-check-circle v-else-if="message.status === 'success'" class="text-green-500" />
                <icon-exclamation-circle v-else-if="message.status === 'error'" class="text-red-500" />
                <span>{{ message.content }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 节点执行状态 -->
        <div v-if="currentNodeStatus" class="flex justify-center mb-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 text-sm">
            <div class="flex items-center space-x-2">
              <icon-loading class="animate-spin text-blue-500" />
              <span class="text-blue-700">正在执行: {{ currentNodeStatus.title }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div class="chat-input p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex space-x-3">
          <a-input
            v-model="inputMessage"
            placeholder="输入消息..."
            :disabled="isExecuting || !conversationStarted"
            class="flex-1"
            size="large"
            @keyup.enter="sendMessage"
          />
          <a-button
            type="primary"
            :disabled="!inputMessage.trim() || isExecuting || !conversationStarted"
            :loading="isExecuting"
            size="large"
            @click="sendMessage"
          >
            <template #icon>
              <icon-send />
            </template>
            发送
          </a-button>
        </div>
        <div v-if="!conversationStarted" class="text-xs text-gray-500 mt-2">请先点击"开始对话"按钮</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { chatflowRunFetch } from '@/apis/workflow'
import { getAppConfig } from '@/apis'
import { Message } from '@arco-design/web-vue'
import { useAppStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useVueFlow } from '@vue-flow/core'
import { useRoute } from 'vue-router'

defineOptions({ name: 'ChatflowPage' })

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  isStreaming?: boolean
  status?: 'running' | 'success' | 'error'
}

interface NodeStatus {
  id: string
  title: string
  status: 'running' | 'success' | 'error'
}

const route = useRoute()
const appStore = useAppStore()
const { currentAppInfo } = storeToRefs(appStore)
const { nodes } = useVueFlow()

// 从路由获取应用ID
const appId = computed(() => route.params.appId as string)

const messages = ref<ChatMessage[]>([])
const inputMessage = ref('')
const isExecuting = ref(false)
const isStarting = ref(false)
const stopLoading = ref(false)
const conversationStarted = ref(false)
const messagesContainer = ref()
const currentNodeStatus = ref<NodeStatus | null>(null)
const variableValues = ref<Record<string, any>>({})
const abortController = ref<AbortController | null>(null)

// 获取应用信息和变量
const appInfo = computed(() => currentAppInfo.value)
const variables = computed(() => {
  const startNode = nodes.value.find((e) => e.type === 'start')
  return startNode?.data?.variables || []
})

// 检查是否可以开始对话
const canStart = computed(() => {
  if (variables.value.length === 0) return true

  for (const variable of variables.value) {
    if (variable.required && !variableValues.value[variable.variable]) {
      return false
    }
  }
  return true
})

// 初始化应用信息和变量默认值
onMounted(async () => {
  // 获取应用配置
  try {
    const appConfig = await getAppConfig({ appId: appId.value })
    appStore.setCurrentAppInfo(appConfig)
  } catch (error) {
    console.error('获取应用配置失败:', error)
    Message.error('获取应用配置失败')
  }

  // 初始化变量默认值
  variables.value.forEach((variable) => {
    if (variable.default) {
      variableValues.value[variable.variable] = variable.default
    }
  })
})

const formatMessage = (content: string) => {
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const startConversation = async () => {
  if (!canStart.value) {
    Message.error('请填写所有必填变量')
    return
  }

  isStarting.value = true

  try {
    // 添加系统消息
    messages.value.push({
      role: 'system',
      content: '对话已开始，您可以开始发送消息',
      status: 'success'
    })

    conversationStarted.value = true
    scrollToBottom()
  } catch (error) {
    console.error('启动对话失败:', error)
    Message.error('启动对话失败')
  } finally {
    isStarting.value = false
  }
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || !conversationStarted.value) return
  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''
  console.log(userMessage, '=========')
  // 添加用户消息
  messages.value.push({
    role: 'user',
    content: userMessage
  })

  // 添加助手流式消息
  messages.value.push({
    role: 'assistant',
    content: '',
    isStreaming: true
  })

  scrollToBottom()
  isExecuting.value = true

  try {
    // 构建输入参数
    const inputs = { ...variableValues.value }
    // 查找查询变量
    const queryVar = variables.value.find(
      (v) => v.variable === 'query' || v.variable === 'input' || v.variable === 'question' || v.variable === 'message'
    )

    if (queryVar) {
      inputs[queryVar.variable] = userMessage
    } else if (variables.value.length > 0) {
      // 如果没有明确的查询变量，使用第一个文本变量
      const firstTextVar = variables.value.find((v) => v.type === 'text-input' || v.type === 'paragraph')
      if (firstTextVar) {
        inputs[firstTextVar.variable] = userMessage
      }
    }
    await executeWorkflow(inputs)
  } catch (error) {
    console.error('发送消息失败:', error)
    handleError('发送消息失败')
  }
}

const executeWorkflow = async (inputs: Record<string, any>) => {
  try {
    abortController.value = chatflowRunFetch(
      appId.value,
      {
        body: { inputs, files: [] }
      },
      {
        onWorkflowStarted: (data) => {
          console.log('工作流开始:', data)
          currentNodeStatus.value = null
        },
        onWorkflowFinished: (data) => {
          console.log('工作流完成:', data)
          handleWorkflowFinished(data)
        },
        onNodeStarted: (data) => {
          console.log('节点开始:', data)
          currentNodeStatus.value = {
            id: data.data.node_id,
            title: data.data.title,
            status: 'running'
          }
          scrollToBottom()
        },
        onNodeFinished: (data) => {
          console.log('节点完成:', data)
          if (currentNodeStatus.value?.id === data.data.node_id) {
            currentNodeStatus.value = null
          }
        },
        onTextChunk: (data) => {
          handleTextChunk(data.data.text)
        },
        onTextReplace: (data) => {
          handleTextReplace(data.data.text)
        },
        onError: (error) => {
          console.error('工作流错误:', error)
          handleError(error)
        }
      }
    )
  } catch (error) {
    console.error('执行工作流时出错:', error)
    handleError('执行失败')
  }
}

const handleTextChunk = (text: string) => {
  const lastMessage = messages.value[messages.value.length - 1]
  if (lastMessage && lastMessage.role === 'assistant') {
    lastMessage.content += text
    lastMessage.isStreaming = true
    scrollToBottom()
  }
}

const handleTextReplace = (text: string) => {
  const lastMessage = messages.value[messages.value.length - 1]
  if (lastMessage && lastMessage.role === 'assistant') {
    lastMessage.content = text
    lastMessage.isStreaming = true
    scrollToBottom()
  }
}

const handleWorkflowFinished = (data: any) => {
  isExecuting.value = false
  currentNodeStatus.value = null

  // 更新最后一条助手消息
  const lastMessage = messages.value[messages.value.length - 1]
  if (lastMessage && lastMessage.role === 'assistant') {
    lastMessage.isStreaming = false
  }

  scrollToBottom()
}

const handleError = (error: string) => {
  isExecuting.value = false
  currentNodeStatus.value = null

  // 更新最后一条助手消息
  const lastMessage = messages.value[messages.value.length - 1]
  if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
    lastMessage.content = '抱歉，处理您的请求时出现了错误。'
    lastMessage.isStreaming = false
  }

  Message.error(error)
  scrollToBottom()
}

const stopExecution = async () => {
  stopLoading.value = true

  try {
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }

    isExecuting.value = false
    currentNodeStatus.value = null

    // 更新流式消息状态
    const lastMessage = messages.value[messages.value.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
      lastMessage.content += '\n\n[执行已停止]'
      lastMessage.isStreaming = false
    }

    scrollToBottom()
  } catch (error) {
    console.error('停止执行失败:', error)
  } finally {
    stopLoading.value = false
  }
}

const clearConversation = () => {
  messages.value = []
  conversationStarted.value = false
  currentNodeStatus.value = null
  isExecuting.value = false
}
</script>

<style scoped lang="scss">
.chatflow-container {
  height: 100%;

  .input-panel {
    min-width: 384px;
    max-width: 384px;
  }

  .typing-indicator {
    display: flex;
    align-items: center;

    span {
      height: 4px;
      width: 4px;
      background-color: #9ca3af;
      border-radius: 50%;
      display: inline-block;
      margin-right: 2px;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
