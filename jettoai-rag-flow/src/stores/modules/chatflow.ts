import { defineStore } from 'pinia'
import type {
  ChatflowMessage,
  ChatflowNodeStatus,
  ChatflowRunState
} from '@/views/app/workflow/hooks/use-chatflow-run-event'

export interface ChatflowConversation {
  id: string
  appId: string
  title: string
  messages: ChatflowMessage[]
  createdAt: number
  updatedAt: number
}

export interface ChatflowState {
  // 当前运行状态
  currentRunState: ChatflowRunState | null

  // 对话历史
  conversations: ChatflowConversation[]

  // 当前对话ID
  currentConversationId: string | null

  // 应用配置
  appConfigs: Record<
    string,
    {
      variables: any[]
      settings: any
    }
  >
}

export const useChatflowStore = defineStore('chatflow', {
  state: (): ChatflowState => ({
    currentRunState: null,
    conversations: [],
    currentConversationId: null,
    appConfigs: {}
  }),

  getters: {
    // 获取当前对话
    currentConversation: (state) => {
      if (!state.currentConversationId) return null
      return state.conversations.find((c) => c.id === state.currentConversationId) || null
    },

    // 获取指定应用的对话列表
    getConversationsByAppId: (state) => (appId: string) => {
      return state.conversations.filter((c) => c.appId === appId)
    },

    // 获取当前运行状态
    isRunning: (state) => {
      return state.currentRunState?.isRunning || false
    },

    // 获取当前消息列表
    currentMessages: (state) => {
      return state.currentRunState?.messages || []
    },

    // 获取当前节点状态
    currentNodeStatuses: (state) => {
      return state.currentRunState?.nodeStatuses || new Map()
    }
  },

  actions: {
    // 设置当前运行状态
    setCurrentRunState(runState: ChatflowRunState) {
      this.currentRunState = runState
    },

    // 清除当前运行状态
    clearCurrentRunState() {
      this.currentRunState = null
    },

    // 创建新对话
    createConversation(appId: string, title?: string): string {
      const conversationId = Date.now().toString()
      const conversation: ChatflowConversation = {
        id: conversationId,
        appId,
        title: title || `对话 ${new Date().toLocaleString()}`,
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now()
      }

      this.conversations.unshift(conversation)
      this.currentConversationId = conversationId

      return conversationId
    },

    // 切换对话
    switchConversation(conversationId: string) {
      const conversation = this.conversations.find((c) => c.id === conversationId)
      if (conversation) {
        this.currentConversationId = conversationId

        // 恢复对话的运行状态
        this.currentRunState = {
          isRunning: false,
          messages: [...conversation.messages],
          nodeStatuses: new Map()
        }
      }
    },

    // 保存当前对话
    saveCurrentConversation() {
      if (!this.currentConversationId || !this.currentRunState) return

      const conversation = this.conversations.find((c) => c.id === this.currentConversationId)
      if (conversation) {
        conversation.messages = [...this.currentRunState.messages]
        conversation.updatedAt = Date.now()

        // 如果对话还没有标题，根据第一条用户消息生成标题
        if (conversation.title.startsWith('对话 ')) {
          const firstUserMessage = conversation.messages.find((m) => m.role === 'user')
          if (firstUserMessage) {
            conversation.title =
              firstUserMessage.content.slice(0, 30) +
              (firstUserMessage.content.length > 30 ? '...' : '')
          }
        }
      }
    },

    // 删除对话
    deleteConversation(conversationId: string) {
      const index = this.conversations.findIndex((c) => c.id === conversationId)
      if (index > -1) {
        this.conversations.splice(index, 1)

        // 如果删除的是当前对话，清除当前状态
        if (this.currentConversationId === conversationId) {
          this.currentConversationId = null
          this.currentRunState = null
        }
      }
    },

    // 清空指定应用的所有对话
    clearConversationsByAppId(appId: string) {
      this.conversations = this.conversations.filter((c) => c.appId !== appId)

      // 如果当前对话被清除，重置状态
      if (this.currentConversation?.appId === appId) {
        this.currentConversationId = null
        this.currentRunState = null
      }
    },

    // 设置应用配置
    setAppConfig(appId: string, config: { variables: any[]; settings: any }) {
      this.appConfigs[appId] = config
    },

    // 获取应用配置
    getAppConfig(appId: string) {
      return this.appConfigs[appId] || { variables: [], settings: {} }
    },

    // 添加消息到当前对话
    addMessageToCurrentConversation(message: Omit<ChatflowMessage, 'id' | 'timestamp'>) {
      if (!this.currentRunState) {
        this.currentRunState = {
          isRunning: false,
          messages: [],
          nodeStatuses: new Map()
        }
      }

      const newMessage: ChatflowMessage = {
        ...message,
        id: Date.now().toString(),
        timestamp: Date.now()
      }

      this.currentRunState.messages.push(newMessage)

      // 自动保存到对话历史
      this.saveCurrentConversation()

      return newMessage
    },

    // 更新最后一条消息
    updateLastMessage(updates: Partial<ChatflowMessage>) {
      if (!this.currentRunState || this.currentRunState.messages.length === 0) return

      const lastMessage = this.currentRunState.messages[this.currentRunState.messages.length - 1]
      Object.assign(lastMessage, updates)

      // 自动保存到对话历史
      this.saveCurrentConversation()
    },

    // 设置运行状态
    setRunning(isRunning: boolean) {
      if (!this.currentRunState) {
        this.currentRunState = {
          isRunning,
          messages: [],
          nodeStatuses: new Map()
        }
      } else {
        this.currentRunState.isRunning = isRunning
      }
    },

    // 设置节点状态
    setNodeStatus(nodeId: string, status: ChatflowNodeStatus) {
      if (!this.currentRunState) {
        this.currentRunState = {
          isRunning: false,
          messages: [],
          nodeStatuses: new Map()
        }
      }

      this.currentRunState.nodeStatuses.set(nodeId, status)
    },

    // 清除节点状态
    clearNodeStatus(nodeId: string) {
      if (this.currentRunState) {
        this.currentRunState.nodeStatuses.delete(nodeId)
      }
    },

    // 清除所有节点状态
    clearAllNodeStatuses() {
      if (this.currentRunState) {
        this.currentRunState.nodeStatuses.clear()
      }
    },

    // 导出对话数据
    exportConversation(conversationId: string) {
      const conversation = this.conversations.find((c) => c.id === conversationId)
      if (!conversation) return null

      return {
        ...conversation,
        exportedAt: Date.now()
      }
    },

    // 导入对话数据
    importConversation(conversationData: any) {
      const conversation: ChatflowConversation = {
        id: conversationData.id || Date.now().toString(),
        appId: conversationData.appId,
        title: conversationData.title,
        messages: conversationData.messages || [],
        createdAt: conversationData.createdAt || Date.now(),
        updatedAt: Date.now()
      }

      // 检查是否已存在相同ID的对话
      const existingIndex = this.conversations.findIndex((c) => c.id === conversation.id)
      if (existingIndex > -1) {
        this.conversations[existingIndex] = conversation
      } else {
        this.conversations.unshift(conversation)
      }

      return conversation.id
    },

    // 获取对话统计信息
    getConversationStats(appId?: string) {
      const conversations = appId
        ? this.conversations.filter((c) => c.appId === appId)
        : this.conversations

      return {
        total: conversations.length,
        totalMessages: conversations.reduce((sum, c) => sum + c.messages.length, 0),
        averageMessagesPerConversation:
          conversations.length > 0
            ? conversations.reduce((sum, c) => sum + c.messages.length, 0) / conversations.length
            : 0,
        lastActivity:
          conversations.length > 0 ? Math.max(...conversations.map((c) => c.updatedAt)) : 0
      }
    }
  },

  // 持久化配置
  persist: {
    key: 'chatflow-store',
    storage: localStorage,
    paths: ['conversations', 'appConfigs'] // 只持久化对话和应用配置，运行状态不持久化
  }
})
