<%
const { apiConfig, routes, utils, config } = it;
const { info, servers, externalDocs } = apiConfig;
const { _, require, formatDescription } = utils;

const server = (servers && servers[0]) || { url: "" };

const descriptionLines = _.compact([
  `@title ${info.title || "No title"}`,
  info.version && `@version ${info.version}`,
  info.license && `@license ${_.compact([
    info.license.name,
    info.license.url && `(${info.license.url})`,
  ]).join(" ")}`,
  info.termsOfService && `@termsOfService ${info.termsOfService}`,
  server.url && `@baseUrl ${server.url}`,
  externalDocs.url && `@externalDocs ${externalDocs.url}`,
  info.contact && `@contact ${_.compact([
    info.contact.name,
    info.contact.email && `<${info.contact.email}>`,
    info.contact.url && `(${info.contact.url})`,
  ]).join(" ")}`,
  info.description && " ",
  info.description && _.replace(formatDescription(info.description), /\n/g, "\n * "),
]);

%>

import http from '@/utils/http'
import type { AxiosRequestConfig } from 'axios'
import { ContentType } from './http-client'
import * as DataContracts from './data-contracts'

<% if (descriptionLines.length && false) { %>
/**
<% descriptionLines.forEach((descriptionLine) => { %>
* <%~ descriptionLine %>

<% }) %>
*/
<% } %>

<% if (routes.outOfModule) { %>
  <% for (const route of routes.outOfModule) { %>

  <%~ includeFile('./procedure-call.ejs', { ...it, route }) %>

  <% } %>
<% } %>

<% if (routes.combined) { %>
  <% for (const { routes: combinedRoutes = [], moduleName } of routes.combined) { %>
// <%= moduleName %>
  <% for (const route of combinedRoutes) { %>

  <%~ includeFile('./procedure-call.ejs', { ...it, route }) %>

  <% } %>
  <% } %>
<% } %>
