<%
const { modelTypes, utils, config } = it;
const { formatDescription, require, _, Ts } = utils;


const buildGenerics = (contract) => {
  if (!contract.genericArgs || !contract.genericArgs.length) return '';

  return '<' + contract.genericArgs.map(({ name, default: defaultType, extends: extendsType }) => {
    return [
      name,
      extendsType && `extends ${extendsType}`,
      defaultType && `= ${defaultType}`,
    ].join('')
  }).join(',') + '>'
}

const dataContractTemplates = {
  enum: (contract) => {
    return `enum ${contract.name} {\r\n${contract.content} \r\n }`;
  },
  interface: (contract) => {
    return `interface ${contract.name}${buildGenerics(contract)} {\r\n${contract.content}}`;
  },
  type: (contract) => {
    return `type ${contract.name}${buildGenerics(contract)} = ${contract.content}`;
  },
}
%>

<% if (config.internalTemplateOptions.addUtilRequiredKeysType) { %>
type <%~ config.Ts.CodeGenKeyword.UtilRequiredKeys %><T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>
<% } %>

<% for (const contract of modelTypes) { %>
  <%~ includeFile('./data-contract-jsdoc.ejs', { ...it, data: { ...contract, ...contract.typeData } }) %>
  <%~ contract.internal ? '' : 'export'%> <%~ (dataContractTemplates[contract.typeIdentifier] || dataContractTemplates.type)(contract) %>


<% } %>
